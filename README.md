# AIfestival

이 프로젝트는 금융 데이터를 활용한 AI 분석 웹 서비스입니다. 처음 방문하는 개발자도 전체 흐름을 이해할 수 있도록 주요 파이프라인과 사용 방법을 정리했습니다.
 
-폴더에서 npm install을 실행하여 의존성을 설치해 주세요

## 특징
- **Next.js 15 기반의 프론트엔드**: `src` 디렉터리의 React 컴포넌트와 페이지에서 사용자 인터페이스를 구성합니다.
- **Python 분석 서비스**: `src/services` 하위에 LSTM 예측, MFI 지수 계산 등 여러 파이썬 스크립트가 존재하며, 주식 데이터를 분석하여 JSON 형태의 결과를 반환합니다.
- **API 라우트**: `src/pages/api` 폴더의 라우트가 Node.js에서 파이썬 스크립트를 실행하며, Server‑Sent Events(SSE)를 사용해 실시간 진행 상황을 전달합니다.
- **RAG 기반 투자 AI 챗봇**: `src/pages/api/ai_chat.ts`가 단계별 상태 머신으로 동작하며 사용자의 질문을 분류하고, 관련 기업과 차트 분석까지 안내합니다.
- **Cloudflare Workers 배포**: `open-next.config.ts`와 `wrangler.toml` 설정을 사용해 Cloudflare 환경으로 빌드 및 배포할 수 있습니다.
- **리포지토리 정비**: 파이프라인과 무관한 더미 파일을 제거해 필요한 코드만 보존했습니다.
 
## 설치
1. Node.js 의존성 설치
   ```bash
   npm install
   ```
2. Python 패키지 설치
   ```bash
   pip install -r requirements.txt
   ```

## 로컬 실행
개발 서버를 실행하면 프론트엔드와 API 라우트를 함께 확인할 수 있습니다.
```bash
npm run dev
```
브라우저에서 `http://localhost:3000`에 접속하면 기본 페이지가 로드됩니다.

## 파이프라인 개요
1. **사용자가 웹 페이지에서 주식 종목을 입력**하면, 프론트엔드 유틸리티(`src/utils/lstm_client.ts`)가 `/api/lstm_prediction` 엔드포인트로 POST 요청을 보냅니다.
2. **API 라우트**(`src/pages/api/lstm_prediction.ts`)는 내부에서 파이썬 스크립트 `lstm_service.py`와 `mfi_service.py`를 병렬로 실행하고, 각각의 표준 출력에서 결과를 받아 실시간으로 SSE 스트림을 통해 클라이언트에 전달합니다.
3. **파이썬 스크립트**는 `data` 폴더의 CSV 파일을 이용해 기술 지표를 계산하고 LSTM 모델을 학습·예측합니다. 결과는 JSON으로 출력됩니다.
4. 두 스크립트가 모두 종료되면 API 라우트가 결과를 병합해 최종 JSON을 클라이언트에 전송합니다. 클라이언트는 이 데이터를 이용해 차트나 예측 정보를 표시합니다.

## AI 챗봇 파이프라인
`ai_chat.ts` 파일은 투자 상담에 특화된 3단계 상태 머신을 구현합니다.

1. **STAGE 0 – 산업 분류**
   - 사용자 질문을 임베딩하여 가장 연관성 높은 산업을 찾고, 점수가 낮을 경우 GPT‑4.1의 분류 결과를 사용합니다.
2. **STAGE 1 – 기업 목록 제시**
   - 선택된 산업에서 5개 회사를 보여 주고 관심 기업을 고르게 합니다. "더보기"를 입력하면 전체 목록을 반환합니다.
3. **STAGE 2 – 차트 분석 확인**
   - 사용자가 특정 기업을 선택하면 차트 분석 진행 여부를 묻고, 동의 시 `/api/speedtraffic_staged`를 호출해 단계별 예측 서비스를 실행합니다.

```
사용자 ↔ ai_chat.ts ↔ 산업 분류 ↔ 기업 선택 ↔ 차트 분석 ↔ 결과 반환
```

